import { Switch, Route } from "wouter";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "@/pages/not-found";
import Home from "@/pages/Home";
import PrivacyPolicy from "@/pages/PrivacyPolicy";
import TermsOfService from "@/pages/TermsOfService";
import Blog from "@/pages/Blog";
import SecurePassword from '@/pages/SecurePassword';
import WeakPasswordRisks from '@/pages/WeakPasswordRisks';
import ManagementTips from '@/pages/ManagementTips';
import CommonMistakes from '@/pages/CommonMistakes';
import DataBreachProtection from '@/pages/DataBreachProtection';
import PhishingProtection from '@/pages/PhishingProtection';
import TwoFactorAuthentication from '@/pages/TwoFactorAuthentication';
import PasswordManagerGuide from '@/pages/PasswordManagerGuide';
import EnterprisePasswordSecurity from '@/pages/EnterprisePasswordSecurity';
import SocialEngineeringProtection from '@/pages/SocialEngineeringProtection';
import ContactUs from '@/pages/ContactUs';
import FAQPage from '@/pages/FAQPage';
import HelpCenter from '@/pages/HelpCenter';
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/privacy-policy" component={PrivacyPolicy} />
      <Route path="/terms-of-service" component={TermsOfService} />
      <Route path="/contact" component={ContactUs} />
      <Route path="/faq" component={FAQPage} />
      <Route path="/help-center" component={HelpCenter} />
      <Route path="/blog" component={Blog} />
      <Route path="/blog/secure-password" component={SecurePassword} />
      <Route path="/blog/weak-password-risks" component={WeakPasswordRisks} />
      <Route path="/blog/management-tips" component={ManagementTips} />
      <Route path="/blog/common-mistakes" component={CommonMistakes} />
      <Route path="/blog/data-breach-protection" component={DataBreachProtection} />
      <Route path="/blog/phishing-protection" component={PhishingProtection} />
      <Route path="/blog/two-factor-authentication" component={TwoFactorAuthentication} />
      <Route path="/blog/password-manager-guide" component={PasswordManagerGuide} />
      <Route path="/blog/enterprise-password-security" component={EnterprisePasswordSecurity} />
      <Route path="/blog/social-engineering-protection" component={SocialEngineeringProtection} />
      <Route path="/:lang" component={Home} />
      <Route path="/:lang/privacy-policy" component={PrivacyPolicy} />
      <Route path="/:lang/terms-of-service" component={TermsOfService} />
      <Route path="/:lang/contact" component={ContactUs} />
      <Route path="/:lang/faq" component={FAQPage} />
      <Route path="/:lang/help-center" component={HelpCenter} />
      <Route path="/:lang/blog" component={Blog} />
      <Route path="/:lang/blog/secure-password" component={SecurePassword} />
      <Route path="/:lang/blog/weak-password-risks" component={WeakPasswordRisks} />
      <Route path="/:lang/blog/management-tips" component={ManagementTips} />
      <Route path="/:lang/blog/common-mistakes" component={CommonMistakes} />
      <Route path="/:lang/blog/data-breach-protection" component={DataBreachProtection} />
      <Route path="/:lang/blog/phishing-protection" component={PhishingProtection} />
      <Route path="/:lang/blog/two-factor-authentication" component={TwoFactorAuthentication} />
      <Route path="/:lang/blog/password-manager-guide" component={PasswordManagerGuide} />
      <Route path="/:lang/blog/enterprise-password-security" component={EnterprisePasswordSecurity} />
      <Route path="/:lang/blog/social-engineering-protection" component={SocialEngineeringProtection} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  const { i18n } = useTranslation();

  // Force default language to English if not explicitly set in URL
  useEffect(() => {
    const pathLang = window.location.pathname.split('/')[1];
    const supportedLangs = ['zh', 'es', 'de', 'fr', 'ja', 'pt', 'ru', 'ar', 'hi'];

    // If no language in path or language is not supported, set to English
    if (!pathLang || !supportedLangs.includes(pathLang)) {
      if (i18n.language !== 'en') {
        i18n.changeLanguage('en');
      }
    }
  }, [i18n]);

  useEffect(() => {
    // Set document direction based on language
    document.documentElement.dir = i18n.dir();
    document.documentElement.lang = i18n.language;

    // Update title and meta description when language changes
    const updateMetaTags = () => {
      document.title = i18n.t('meta.title');
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', i18n.t('meta.description'));
      }

      const ogTitle = document.querySelector('meta[property="og:title"]');
      const ogDescription = document.querySelector('meta[property="og:description"]');

      if (ogTitle) {
        ogTitle.setAttribute('content', i18n.t('meta.title'));
      }

      if (ogDescription) {
        ogDescription.setAttribute('content', i18n.t('meta.description'));
      }
    };

    updateMetaTags();
  }, [i18n.language]);

  return (
    <TooltipProvider>
      <Toaster />
      <Router />
    </TooltipProvider>
  );
}

export default App;
