import { ArrowLeft } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'wouter';

interface BackButtonProps {
  className?: string;
}

const BackButton = ({ className = '' }: BackButtonProps) => {
  const { t } = useTranslation();
  const [, setLocation] = useLocation();

  const handleBack = () => {
    setLocation('/');
  };

  return (
    <button
      onClick={handleBack}
      className={`inline-flex items-center space-x-2 text-primary hover:text-primary-dark transition-colors mb-6 ${className}`}
    >
      <ArrowLeft className="h-4 w-4" />
      <span>{t('common.backToHome')}</span>
    </button>
  );
};

export default BackButton;
