import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import FAQ from '@/components/FAQ';
import BackButton from '@/components/ui/BackButton';

const FAQPage = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Helmet>
        <title>{t('faqPage.title')} - {t('footer.name')}</title>
        <meta name="description" content={t('faqPage.subtitle')} />
      </Helmet>
      
      <div className="container mx-auto px-4 py-8">
        <BackButton />
        
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {t('faqPage.title')}
            </h1>
            <p className="text-xl text-gray-600">
              {t('faqPage.subtitle')}
            </p>
          </div>

          <FAQ />
        </div>
      </div>
    </div>
  );
};

export default FAQPage;
