import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import BackButton from '@/components/ui/BackButton';

const PrivacyPolicy = () => {
  const { t, i18n } = useTranslation();

  return (
    <>
      <Helmet>
        {/* 基本SEO元标签 */}
        <title>{t('privacyPolicy.title')} | {t('footer.name')}</title>
        <meta name="description" content={t('privacyPolicy.introduction')} />
        <meta name="keywords" content="隐私政策, 数据保护, 个人信息, 密码生成器隐私" />

        {/* 规范链接 - 防止重复内容 */}
        <link rel="canonical" href={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/privacy-policy`} />

        {/* Open Graph 标签 */}
        <meta property="og:title" content={`${t('privacyPolicy.title')} | ${t('footer.name')}`} />
        <meta property="og:description" content={t('privacyPolicy.introduction')} />
        <meta property="og:type" content="article" />
        <meta property="og:url" content={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/privacy-policy`} />
        <meta property="og:image" content="https://password-generator.me/og-image.jpg" />

        {/* Twitter 卡片标签 */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={`${t('privacyPolicy.title')} | ${t('footer.name')}`} />
        <meta name="twitter:description" content={t('privacyPolicy.introduction')} />
        <meta name="twitter:image" content="https://password-generator.me/og-image.jpg" />
        <meta name="twitter:site" content="@password_generator_me" />

        {/* 面包屑导航结构化数据 */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": t('footer.name'),
                "item": `https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}`
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": t('privacyPolicy.title'),
                "item": `https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/privacy-policy`
              }
            ]
          })}
        </script>
        <meta property="og:locale" content={i18n.language} />
        <meta property="og:site_name" content={t('footer.name')} />

        {/* JSON-LD 结构化数据 */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": `${t('privacyPolicy.title')} | ${t('footer.name')}`,
            "description": t('privacyPolicy.introduction'),
            "url": `https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}/privacy-policy`,
            "datePublished": "2025-05-15",
            "dateModified": "2025-05-15",
            "publisher": {
              "@type": "Organization",
              "name": t('footer.name'),
              "logo": {
                "@type": "ImageObject",
                "url": "https://password-generator.me/logo.png"
              }
            },
            "inLanguage": i18n.language
          })}
        </script>
      </Helmet>

      <div className="flex flex-col min-h-screen">
        <Header />

        <main className="container mx-auto px-4 py-10 max-w-4xl flex-grow">
          <BackButton />
          <section className="prose prose-lg mx-auto dark:prose-invert">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              {t('privacyPolicy.title')}
            </h1>

            <p className="text-gray-500 mb-8">
              {t('privacyPolicy.lastUpdated')}
            </p>

            <p className="mb-8">
              {t('privacyPolicy.introduction')}
            </p>

            {t('privacyPolicy.sections', { returnObjects: true }).map((section: any, index: number) => (
              <div key={index} className="mb-8">
                <h2 className="text-2xl font-bold mb-4">
                  {section.title}
                </h2>
                <p>
                  {section.content}
                </p>
              </div>
            ))}
          </section>
        </main>

        <Footer />
      </div>
    </>
  );
};

export default PrivacyPolicy;
