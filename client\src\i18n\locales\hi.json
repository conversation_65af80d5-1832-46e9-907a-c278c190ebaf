{"meta": {"title": "सिक्योरपास - यादृच्छिक पासवर्ड जनरेटर", "description": "हमारे मुफ्त यादृच्छिक पासवर्ड जनरेटर टूल के साथ अपने ऑनलाइन खातों को सुरक्षित रखने के लिए मजबूत और सुरक्षित पासवर्ड बनाएं।", "keywords": "पासवर्ड जनरेटर, यादृच्छिक पासवर्ड जनरेटर, सुरक्षित पासवर्ड जनरेटर, मजबूत पासवर्ड जनरेटर, मुफ्त ऑनलाइन पासवर्ड जनरेटर, मजबूत पासवर्ड निर्माता, सुरक्षित पासवर्ड टूल, मजबूत पासवर्ड कैसे बनाएं, मुफ्त पासवर्ड जनरेटर ऑनलाइन, यादृच्छिक पासवर्ड निर्माता, पासवर्ड निर्माण टूल, सुरक्षित पासवर्ड बिल्डर, पासवर्ड जेनरेशन टूल, ऑनलाइन मजबूत पासवर्ड बनाएं, पासवर्ड सुरक्षा टूल, यादृच्छिक सुरक्षित पासवर्ड, मुफ्त पासवर्ड जनरेटर, ऑनलाइन पासवर्ड निर्माता, पासवर्ड निर्माता, पासवर्ड बिल्डर, सुरक्षित पासवर्ड, मजबूत पासवर्ड, पासवर्ड सुरक्षा"}, "header": {"about": "हमारे बारे में", "blog": "ब्लॉग", "downloadApp": "ऐप डाउनलोड करें"}, "home": {"title": "यादृच्छिक पासवर्ड जनरेटर", "subtitle": "अपने ऑनलाइन खाते को सुरक्षित रखने के लिए मजबूत और सुरक्षित पासवर्ड बनाएं।"}, "passwordGenerator": {"generatedPassword": "उत्पन्न पासवर्ड", "copyPassword": "पासवर्ड को क्लिपबोर्ड पर कॉपी करें", "copiedToClipboard": "पासवर्ड क्लिपबोर्ड पर कॉपी किया गया!", "copyFailed": "कॉपी करने में विफल। कृपया पुनः प्रयास करें।", "passwordLength": "पासवर्ड की लंबाई", "decreaseLength": "पासवर्ड की लंबाई कम करें", "increaseLength": "पासवर्ड की लंबाई बढ़ाएं", "charactersUsed": "उपयोग किए गए वर्ण", "generateNew": "नया पासवर्ड बनाएं"}, "passwordStrength": {"veryweak": "बहुत कमजोर", "weak": "कम<PERSON>ोर", "medium": "मध्यम", "strong": "मजबूत", "verystrong": "बहुत मजबूत"}, "passwordStrengthInfo": {"title": "पासवर्ड को मजबूत क्या बनाता है?", "length": {"title": "लंबाई", "description": "पासवर्ड जितना लंबा होगा, उतना ही सुरक्षित होगा। एक मजबूत पासवर्ड में कम से कम 10 वर्ण होने चाहिए।"}, "complex": {"title": "जटिलता", "description": "मजबूत पासवर्ड वर्णों की एक अप्रत्याशित स्ट्रिंग बनाने के लिए अक्षरों, संख्याओं, केस और प्रतीकों के संयोजन का उपयोग करते हैं।"}, "unique": {"title": "अद्वितीय", "description": "हैकिंग की स्थिति में भेद्यता कम करने के लिए एक मजबूत पासवर्ड प्रत्येक खाते के लिए अद्वितीय होना चाहिए।"}}, "securityTips": {"title": "पासवर्ड सुरक्षा के लिए सर्वोत्तम अभ्यास", "different": "अपने प्रत्येक महत्वपूर्ण खाते के लिए अलग-अलग पासवर्ड का उपयोग करें", "manager": "अपने क्रेडेंशियल्स को स्टोर करने और ऑटो-फिल करने के लिए पासवर्ड मैनेजर का उपयोग करें", "twoFactor": "जब भी संभव हो, दो-कारक प्रमाणीकरण सक्षम करें", "breach": "यदि आपके द्वारा उपयोग की जाने वाली कोई सेवा उल्लंघन की सूचना देती है, तो तुरंत पासवर्ड बदल दें", "personal": "अपने पासवर्ड में व्यक्तिगत जानकारी का उपयोग करने से बचें", "passphrase": "बेहतर याद रखने के लिए पासफ़्रेज़ (यादृच्छिक शब्दों का एक क्रम) का उपयोग करने पर विचार करें"}, "faq": {"title": "पासवर्ड जनरेटर के बारे में अक्सर पूछे जाने वाले प्रश्न", "items": {"safe": {"question": "क्या इस पासवर्ड जनरेटर का उपयोग करना सुरक्षित है?", "answer": "बिल्कुल! हमारा यादृच्छिक पासवर्ड जनरेटर संख्याओं, अक्षरों और प्रतीकों से मिलकर एक यादृच्छिक पासवर्ड बनाने के लिए गणितीय एंट्रोपी का उपयोग करता है। उत्पन्न वर्ण पूरी तरह से यादृच्छिक हैं और इंटरनेट पर प्रसारित नहीं होंगे, जो जनरेशन प्रक्रिया के दौरान सबसे सुरक्षित पासवर्ड प्रदान करते हैं। कोई भी आपके निजी, व्यक्तिगत पासवर्ड को नहीं देख सकता है।"}, "why": {"question": "मुझे पासवर्ड जनरेटर का उपयोग क्यों करना चाहिए?", "answer": "कंप्यूटर मानव-निर्मित पासवर्ड को जल्दी से अनुमान लगा सकते हैं। एक पारंपरिक डेस्कटॉप कंप्यूटर का उपयोग करने वाला हैकर सेकंडों में अरबों अलग-अलग पासवर्ड का परीक्षण कर सकता है। हमारा फ्री पासवर्ड जनरेटर वास्तव में सुरक्षित, यादृच्छिक पासवर्ड बनाने के लिए एल्गोरिथम से गणितीय यादृच्छिकता पर निर्भर करता है, जिन्हें तोड़ना बहुत कठिन होता है।"}, "unique": {"question": "क्या मुझे हर खाते के लिए एक अद्वितीय पासवर्ड की आवश्यकता है?", "answer": "हां, प्रत्येक ऑनलाइन खाते के लिए एक अद्वितीय पासवर्ड होना महत्वपूर्ण है। सुरक्षा उल्लंघन के कारण पासवर्ड लीक होने पर, हैकर अक्सर उन्हें डेटाबेस में रखते हैं। कई साइटों पर एक ही पासवर्ड का उपयोग करने का मतलब है कि यदि एक साइट से समझौता किया जाता है, तो आपके सभी खाते जोखिम में हो सकते हैं।"}, "worst": {"question": "10 सबसे खराब पासवर्ड क्या हैं?", "answer": "<p>आम पासवर्ड दिखाते हैं कि यादृच्छिक वर्ण उत्पन्न करने में मनुष्य कितने भयानक हैं:</p><ul class='list-disc pl-5 mt-2 space-y-1'><li>123456</li><li>Password</li><li>12345678</li><li>Qwerty</li><li>12345</li><li>123456789</li><li>Letmein</li><li>1234567</li><li>Football</li><li>iloveyou</li></ul>"}, "requirements": {"question": "एक मजबूत पासवर्ड के लिए क्या आवश्यकताएं हैं?", "answer": "<p>एक मजबूत पासवर्ड होना चाहिए:</p><ul class='list-disc pl-5 mt-2 space-y-1'><li>कम से कम 12 वर्ण लंबा</li><li>अपरकेस और लोअरकेस अक्षरों का मिश्रण शामिल हो</li><li>संख्याएँ शामिल हों</li><li>विशेष वर्ण शामिल हों (!@#$%&)</li><li>व्यक्तिगत जानकारी पर आधारित न हो</li><li>आम शब्दकोश शब्द शामिल न हों</li><li>प्रत्येक खाते के लिए अद्वितीय हो</li></ul>"}}}, "footer": {"tagline": "अपने ऑनलाइन खातों को सुरक्षित रखने के लिए मजबूत, सुरक्षित और यादृच्छिक पासवर्ड बनाएं।", "products": {"title": "उत्पाद", "passwordManager": "पासवर्ड मैनेजर", "passwordChecker": "पासवर्ड चेकर", "passwordGenerator": "पासवर्ड जनरेटर", "chromeExtension": "क्रोम एक्सटेंशन"}, "resources": {"title": "संसाधन", "blog": "ब्लॉग", "securityTips": "सुरक्षा टिप्स", "documentation": "दस्तावेज़ीकरण", "faq": "अक्सर पूछे जाने वाले प्रश्न", "contactUs": "हमसे संपर्क करें", "helpCenter": "सहायता केंद्र"}, "company": {"title": "कंपनी", "aboutUs": "हमारे बारे में", "contact": "संपर्क करें", "privacyPolicy": "गोपनीयता नीति", "terms": "सेवा की शर्तें"}, "copyright": "सर्वाधिकार सुरक्षित।", "name": "पासवर्ड जनरेटर"}, "privacyPolicy": {"title": "गोपनीयता नीति", "lastUpdated": "अंतिम अपडेट: 15 मई, 2025", "introduction": "यह गोपनीयता नीति बताती है कि पासवर्ड जनरेटर ('हम', 'हमारा', या 'हमें') आपके द्वारा हमारी वेबसाइट और सेवाओं का उपयोग करते समय आपके बारे में जानकारी कैसे एकत्र, उपयोग और साझा करता है। हम आपकी गोपनीयता की रक्षा करने और हमारी वेबसाइट पर आपको सकारात्मक अनुभव सुनिश्चित करने के लिए प्रतिबद्ध हैं।", "sections": [{"title": "हम जो जानकारी एकत्र करते हैं", "content": "हमारा पासवर्ड जनरेटर पूरी तरह से आपके ब्राउज़र में काम करता है। हम आपके द्वारा उत्पन्न पासवर्ड को एकत्र, संग्रहीत या प्रेषित नहीं करते हैं। आपकी सुरक्षा हमारी प्राथमिकता है - पासवर्ड स्थानीय रूप से आपके डिवाइस पर उत्पन्न किए जाते हैं और कभी भी हमारे सर्वर पर नहीं भेजे जाते हैं। हम अपनी सेवा को बेहतर बनाने के लिए पृष्ठ विज़िट और सुविधा उपयोग जैसे अनाम उपयोग डेटा एकत्र कर सकते हैं।"}, {"title": "हम जानकारी का उपयोग कैसे करते हैं", "content": "हम एकत्रित की गई जानकारी का उपयोग अपनी वेबसाइट को संचालित और सुधारने, उपयोग पैटर्न का विश्लेषण करने और उपयोगकर्ता अनुभव को बढ़ाने के लिए करते हैं। एकत्रित किया गया कोई भी अनाम डेटा केवल हमारी सेवाओं को बेहतर बनाने और यह समझने के लिए उपयोग किया जाता है कि उपयोगकर्ता हमारे टूल के साथ कैसे बातचीत करते हैं।"}, {"title": "कुकीज़ और ट्रैकिंग", "content": "हम आपकी भाषा प्राथमिकताओं और सेटिंग्स को याद रखने के लिए कुकीज़ का उपयोग करते हैं। ये कुकीज़ आपको एक व्यक्तिगत अनुभव प्रदान करने के लिए आवश्यक हैं। आप अपने ब्राउज़र को कुकीज़ को अस्वीकार करने के लिए कॉन्फ़िगर कर सकते हैं, लेकिन इससे हमारी वेबसाइट की कुछ कार्यक्षमता सीमित हो सकती है।"}, {"title": "डेटा सुरक्षा", "content": "हम किसी भी जानकारी की रक्षा के लिए उद्योग-मानक सुरक्षा उपायों को लागू करते हैं जिसे हम एकत्र कर सकते हैं। हालांकि, कृपया ध्यान दें कि इंटरनेट पर संचरण का कोई भी तरीका 100% सुरक्षित नहीं है। हम आपकी जानकारी की रक्षा के लिए अपनी सुरक्षा प्रथाओं की लगातार समीक्षा और सुधार करते हैं।"}, {"title": "तृतीय-पक्ष सेवाएँ", "content": "हमारी वेबसाइट में तृतीय-पक्ष वेबसाइटों या सेवाओं के लिंक शामिल हो सकते हैं। हम इन तृतीय पक्षों की गोपनीयता प्रथाओं के लिए जिम्मेदार नहीं हैं। हम आपको हमारी वेबसाइट पर लिंक के माध्यम से आप जिन भी तृतीय-पक्ष वेबसाइटों पर जाते हैं, उनकी गोपनीयता नीतियों को पढ़ने के लिए प्रोत्साहित करते हैं।"}, {"title": "इस नीति में परिवर्तन", "content": "हम समय-समय पर इस गोपनीयता नीति को अपडेट कर सकते हैं ताकि हमारी प्रथाओं में परिवर्तन या अन्य परिचालन, कानूनी या नियामक कारणों को प्रतिबिंबित किया जा सके। हम आपको इस पृष्ठ पर नई गोपनीयता नीति पोस्ट करके और 'अंतिम अपडेट' तिथि को अपडेट करके किसी भी महत्वपूर्ण परिवर्तन के बारे में सूचित करेंगे।"}, {"title": "हमसे संपर्क करें", "content": "यदि आपके पास इस गोपनीयता नीति या हमारी डेटा प्रथाओं के बारे में कोई प्रश्न हैं, तो कृपया हमसे संपर्क करें। हम आपकी गोपनीयता प्रथाओं के बारे में आपकी किसी भी चिंता को संबोधित करने के लिए प्रतिबद्ध हैं।"}]}, "termsOfService": {"title": "सेवा की शर्तें", "lastUpdated": "अंतिम अपडेट: 15 मई, 2025", "introduction": "ये सेवा की शर्तें ('शर्तें') पासवर्ड जनरेटर वेबसाइट और सेवाओं तक आपकी पहुंच और उपयोग को नियंत्रित करती हैं। हमारी सेवा तक पहुंचकर या उसका उपयोग करके, आप इन शर्तों से बाध्य होने के लिए सहमत होते हैं। कृपया हमारी सेवाओं का उपयोग करने से पहले इन्हें ध्यान से पढ़ें।", "sections": [{"title": "सेवा का उपयोग", "content": "आप व्यक्तिगत या व्यावसायिक उद्देश्यों के लिए हमारी पासवर्ड जनरेटर सेवा का उपयोग कर सकते हैं। आप अपने द्वारा उत्पन्न किसी भी पासवर्ड की सुरक्षा बनाए रखने के लिए जिम्मेदार हैं। हमारा टूल आपको मजबूत, सुरक्षित पासवर्ड बनाने में मदद करने के लिए डिज़ाइन किया गया है, लेकिन पासवर्ड प्रबंधन की अंतिम जिम्मेदारी आपकी है।"}, {"title": "बौद्धिक संपदा", "content": "पासवर्ड जनरेटर वेबसाइट और इसकी मूल सामग्री, सुविधाएँ और कार्यक्षमता हमारे स्वामित्व में हैं और अंतर्राष्ट्रीय कॉपीराइट, ट्रेडमार्क और अन्य बौद्धिक संपदा कानूनों द्वारा संरक्षित हैं। आप हमारी अनुमति के बिना हमारी वेबसाइट से किसी भी सामग्री को पुन: प्रस्तुत, वितरित, संशोधित, व्युत्पन्न कार्य बनाने, सार्वजनिक रूप से प्रदर्शित करने या उपयोग नहीं कर सकते हैं।"}, {"title": "वारंटी का अस्वीकरण", "content": "हमारी सेवा 'जैसी है' और 'जैसी उपलब्ध है' के आधार पर बिना किसी प्रकार की वारंटी के प्रदान की जाती है, चाहे वह स्पष्ट हो या निहित हो। हम गारंटी नहीं देते हैं कि सेवा निर्बाध, सुरक्षित या त्रुटि-मुक्त होगी। हालांकि हम एक विश्वसनीय सेवा प्रदान करने का प्रयास करते हैं, हम यह गारंटी नहीं दे सकते हैं कि हमारा पासवर्ड जनरेटर आपकी सभी विशिष्ट आवश्यकताओं को पूरा करेगा।"}, {"title": "देयता की सीमा", "content": "किसी भी स्थिति में, हम किसी भी अप्रत्यक्ष, आकस्मिक, विशेष, परिणामी या दंडात्मक क्षति के लिए उत्तरदायी नहीं होंगे, जिसमें बिना किसी सीमा के, लाभ, डेटा, उपयोग, सद्भावना या अन्य अमूर्त नुकसान शामिल हैं। हम किसी भी क्षति या सुरक्षा उल्लंघन के लिए जिम्मेदार नहीं हैं जो हमारी सेवा के आपके उपयोग या हमारे टूल द्वारा उत्पन्न किसी भी पासवर्ड से हो सकता है।"}, {"title": "शासी कानून", "content": "ये शर्तें उस क्षेत्राधिकार के कानूनों के अनुसार शासित और व्याख्या की जाएंगी जिसमें हम संचालित होते हैं, बिना इसके कानून के प्रावधानों के संघर्ष पर विचार किए। इन शर्तों या हमारी सेवा के आपके उपयोग से उत्पन्न होने वाले किसी भी विवाद का समाधान हमारे क्षेत्राधिकार के न्यायालयों में किया जाएगा।"}, {"title": "शर्तों में परिवर्तन", "content": "हम अपने विवेकाधिकार पर किसी भी समय इन शर्तों को संशोधित या बदलने का अधिकार सुरक्षित रखते हैं। परिवर्तनों के लिए समय-समय पर इन शर्तों की समीक्षा करना आपकी जिम्मेदारी है। शर्तों में किसी भी परिवर्तन के बाद हमारी सेवा का आपका निरंतर उपयोग नई शर्तों की आपकी स्वीकृति का गठन करता है।"}, {"title": "हमसे संपर्क करें", "content": "यदि आपके पास इन शर्तों के बारे में कोई प्रश्न हैं, तो कृपया हमसे संपर्क करें। हम आपकी सेवा की शर्तों और वे आपके पासवर्ड जनरेटर के उपयोग को कैसे प्रभावित करते हैं, के बारे में आपकी किसी भी चिंता को संबोधित करने के लिए प्रतिबद्ध हैं।"}]}, "blog": {"button": "और लेख", "title": "पासवर्ड सुरक्षा ब्लॉग", "intro": "सुरक्षित पासवर्ड बनाने और प्रबंधित करने के लिए टिप्स और सर्वोत्तम प्रथाओं की खोज करें।", "securePassword": {"title": "सुरक्षित पासवर्ड कैसे बनाएं", "content": "एक सुरक्षित पासवर्ड हैकर्स के खिलाफ पहली रक्षा पंक्ति है। जानें कि अपने खातों की रक्षा के लिए मजबूत पासवर्ड कैसे बनाएं।", "details": ["अपर और लोअर केस अक्षरों, संख्याओं और प्रतीकों का मिश्रण उपयोग करें", "जन्मतिथि या नाम जैसी व्यक्तिगत जानकारी से बचें", "पासवर्ड को पर्याप्त लंबा रखें - कम से कम 12 वर्ण", "प्रत्येक खाते के लिए अद्वितीय पासवर्ड का उपयोग करें", "सामान्य शब्दों और अनुमानित अनुक्रमों से बचें"]}, "weakPasswordRisks": {"title": "कमजोर पासवर्ड का उपयोग करने के जोखिम", "content": "कमजोर पासवर्ड आपके खातों की सुरक्षा में एक प्रमुख कमजोरी हैं। जोखिमों और उन्हें कैसे टाला जा सकता है, यह जानें।", "details": ["हैकर्स द्वारा खातों में आसानी से सेंध लगाई जा सकती है", "पहचान की चोरी और व्यक्तिगत जानकारी के जोखिम", "संवेदनशील डेटा तक अनधिकृत पहुंच", "संभावित वित्तीय नुकसान और प्रतिष्ठा को क्षति", "व्यक्तिगत गोपनीयता और सुरक्षा के लिए जोखिम"]}, "managementTips": {"title": "पासवर्ड प्रबंधन के टिप्स", "content": "अपने खातों की सुरक्षा बनाए रखने के लिए प्रभावी पासवर्ड प्रबंधन महत्वपूर्ण है। सर्वोत्तम प्रथाएं सीखें।", "details": ["विश्वसनीय पासवर्ड मैनेजर का उपयोग करें", "नियमित रूप से पासवर्ड अपडेट करें", "जहां उपलब्ध हो, दो-कारक प्रमाणीकरण सक्षम करें", "अपने पासवर्ड व्यवस्थित रूप से व्यवस्थित करें", "अपने पासवर्ड का सुरक्षित बैकअप रखें"]}, "commonMistakes": {"title": "2024 में पासवर्ड की सामान्य गलतियां", "content": "कई लोग पासवर्ड प्रबंधन में महत्वपूर्ण गलतियां करते हैं। सामान्य गलतियों और उन्हें कैसे टाला जा सकता है, यह जानें।", "details": ["सभी खातों के लिए एक ही पासवर्ड का उपयोग करना", "आसानी से अनुमान लगाए जा सकने वाले पासवर्ड चुनना", "दूसरों के साथ पासवर्ड साझा करना", "असुरक्षित तरीके से पासवर्ड स्टोर करना", "नियमित पासवर्ड अपडेट की अनदेखी करना"]}, "dataBreachProtection": {"title": "डेटा उल्लंघन सुरक्षा की संपूर्ण गाइड", "content": "डेटा उल्लंघन तब होता है जब संवेदनशील, संरक्षित या गोपनीय डेटा को अनधिकृत व्यक्तियों द्वारा एक्सेस, प्रकट या चुराया जाता है। हमारे डिजिटल युग में, डेटा उल्लंघन तेजी से आम हो रहे हैं, जो प्रमुख तकनीकी कंपनियों से लेकर छोटे व्यवसायों तक सभी आकार के संगठनों को प्रभावित कर रहे हैं। अपने डेटा की सुरक्षा कैसे करें और उल्लंघन होने पर उचित प्रतिक्रिया कैसे दें, यह समझना आपकी डिजिटल सुरक्षा बनाए रखने के लिए महत्वपूर्ण है।\n\nHave I Been Pwned जैसी सेवाओं का उपयोग करके नियमित रूप से जांचें कि क्या आपके खाते ज्ञात डेटा उल्लंघनों में समझौता किए गए हैं। जब कोई उल्लंघन खोजा जाता है, तो तुरंत प्रभावित खातों के लिए पासवर्ड बदलें, दो-कारक प्रमाणीकरण सक्षम करें, और खाता गतिविधि की बारीकी से निगरानी करें। रोकथाम के लिए, मजबूत अनूठे पासवर्ड का उपयोग करें, व्यक्तिगत जानकारी साझा करना सीमित करें, सॉफ़्टवेयर को अपडेट रखें, और खतरों से बचाव के लिए प्रतिष्ठित सुरक्षा सॉफ़्टवेयर का उपयोग करें।", "detail1": "नियमित रूप से जांचें कि क्या आपके खाते ज्ञात डेटा उल्लंघन डेटाबेस में दिखाई देते हैं।", "detail2": "उल्लंघन खोजे जाने पर तुरंत पासवर्ड बदलें और 2FA सक्षम करें।", "detail3": "सोशल मीडिया और वेबसाइटों पर साझा की जाने वाली व्यक्तिगत जानकारी को सीमित करें।", "detail4": "विश्वसनीय सुरक्षा सॉफ़्टवेयर के साथ सॉफ़्टवेयर और सिस्टम को अपडेट रखें।"}, "phishingProtection": {"title": "फिशिंग हमले की पहचान और सुरक्षा", "content": "फिशिंग एक प्रकार का सामाजिक इंजीनियरिंग हमला है जहां साइबर अपराधी पासवर्ड, क्रेडिट कार्ड नंबर या व्यक्तिगत पहचान विवरण जैसी संवेदनशील जानकारी चुराने के लिए विश्वसनीय संस्थाओं का रूप धारण करते हैं। ये हमले आमतौर पर ईमेल, टेक्स्ट संदेश, सोशल मीडिया या नकली वेबसाइटों के माध्यम से होते हैं जो उपयोगकर्ताओं को गोपनीय जानकारी प्रकट करने के लिए धोखा देने के लिए डिज़ाइन किए गए हैं।\n\nफिशिंग हमलों की पहचान करने की कुंजी सतर्कता बनाए रखना है। संदिग्ध डोमेन के लिए भेजने वाले पते की जांच करें, वर्तनी और व्याकरण की त्रुटियों की तलाश करें, क्लिक करने से पहले लिंक की प्रामाणिकता सत्यापित करें, और संदिग्ध अटैचमेंट डाउनलोड करने से बचें। वैध कंपनियां शायद ही कभी ईमेल के माध्यम से पासवर्ड या संवेदनशील जानकारी मांगती हैं। संदेह की स्थिति में, आधिकारिक चैनलों के माध्यम से संगठन से सीधे संपर्क करें। अतिरिक्त सुरक्षा के लिए एंटी-फिशिंग टूल का उपयोग करें और अपने ब्राउज़र को अपडेट रखें।", "detail1": "संदिग्ध संकेतों के लिए भेजने वाले पते और सामग्री की सावधानीपूर्वक जांच करें।", "detail2": "ईमेल लिंक के माध्यम से कभी भी संवेदनशील जानकारी दर्ज न करें; आधिकारिक वेबसाइटों पर सीधे जाएं।", "detail3": "ब्राउज़र एंटी-फिशिंग सुविधाओं और सुरक्षा एक्सटेंशन का उपयोग करें।", "detail4": "जब संदिग्ध ईमेल आएं, तो आधिकारिक चैनलों के माध्यम से सत्यापित करें।"}, "twoFactorAuthentication": {"title": "दो-कारक प्रमाणीकरण सेटअप की संपूर्ण गाइड", "content": "दो-कारक प्रमाणीकरण (2FA) एक सुरक्षा उपाय है जिसके लिए उपयोगकर्ताओं को खाते तक पहुंचने के लिए दो अलग प्रमाणीकरण कारक प्रदान करने की आवश्यकता होती है। भले ही पासवर्ड समझौता हो जाए, हमलावरों को लॉग इन करने के लिए अभी भी दूसरे सत्यापन कारक की आवश्यकता होती है, जो खाता सुरक्षा में काफी सुधार करता है। सामान्य 2FA विधियों में SMS कोड, प्रमाणीकरण ऐप्स, हार्डवेयर टोकन और बायोमेट्रिक सत्यापन शामिल हैं।\n\n2FA सेट करते समय, जब संभव हो तो SMS के बजाय प्रमाणीकरण ऐप्स (जैसे Google Authenticator या Authy) का उपयोग करें, क्योंकि SMS को इंटरसेप्ट किया जा सकता है। महत्वपूर्ण खातों (ईमेल, बैंकिंग, सोशल मीडिया) के लिए 2FA सक्षम करने को प्राथमिकता दें। हमेशा बैकअप कोड सेव करें और उन्हें सुरक्षित रूप से स्टोर करें यदि आपका फोन खो जाता है या क्षतिग्रस्त हो जाता है। सभी महत्वपूर्ण खाते सुरक्षित रहें यह सुनिश्चित करने के लिए नियमित रूप से अपनी 2FA सेटिंग्स की समीक्षा और अपडेट करें।", "detail1": "ईमेल, बैंकिंग और महत्वपूर्ण खातों के लिए 2FA सक्षम करने को प्राथमिकता दें।", "detail2": "बेहतर सुरक्षा के लिए SMS सत्यापन के बजाय प्रमाणीकरण ऐप्स का उपयोग करें।", "detail3": "डिवाइस खोने या क्षति की स्थिति में बैकअप कोड को सुरक्षित रूप से स्टोर करें।", "detail4": "निरंतर सुरक्षा के लिए नियमित रूप से 2FA सेटिंग्स की समीक्षा और अपडेट करें।"}, "passwordManagerGuide": {"title": "पासवर्ड मैनेजर चयन और उपयोग गाइड", "content": "पासवर्ड मैनेजर एक सॉफ़्टवेयर टूल है जो विशेष रूप से पासवर्ड जेनरेट करने, स्टोर करने और प्रबंधित करने के लिए डिज़ाइन किया गया है। यह प्रत्येक खाते के लिए अनूठे मजबूत पासवर्ड बना सकता है और उन्हें सुरक्षित रूप से स्टोर कर सकता है, जिससे उपयोगकर्ताओं को केवल एक मास्टर पासवर्ड याद रखने की आवश्यकता होती है। व्यक्तिगत साइबर सुरक्षा बढ़ाने के लिए सही पासवर्ड मैनेजर चुनना महत्वपूर्ण है।", "detail1": "एंड-टू-एंड एन्क्रिप्शन और जीरो-नॉलेज आर्किटेक्चर वाले पासवर्ड मैनेजर चुनें।", "detail2": "सभी डिवाइसों में उपयोगिता सुनिश्चित करने के लिए क्रॉस-प्लेटफॉर्म समर्थन पर विचार करें।", "detail3": "महत्वपूर्ण खातों के साथ माइग्रेशन शुरू करें, धीरे-धीरे सभी पासवर्ड अपडेट करें।", "detail4": "अपने पासवर्ड मैनेजर खाते के लिए दो-कारक प्रमाणीकरण सक्षम करें।"}, "enterprisePasswordSecurity": {"title": "एंटरप्राइज़ पासवर्ड सुरक्षा रणनीति और प्रबंधन", "content": "एंटरप्राइज़ पासवर्ड सुरक्षा संगठनात्मक साइबर सुरक्षा की नींव बनाती है, जिसके लिए कॉर्पोरेट डेटा और सिस्टम की सुरक्षा के लिए व्यापक पासवर्ड नीतियों की आवश्यकता होती है। प्रभावी एंटरप्राइज़ पासवर्ड रणनीतियों में पासवर्ड जटिलता आवश्यकताएं, नियमित रोटेशन नियम, मल्टी-फैक्टर प्रमाणीकरण, कर्मचारी प्रशिक्षण और तकनीकी समाधान शामिल होने चाहिए।", "detail1": "जटिलता और रोटेशन आवश्यकताओं सहित स्पष्ट एंटरप्राइज़ पासवर्ड नीतियां स्थापित करें।", "detail2": "एंटरप्राइज़-ग्रेड पासवर्ड प्रबंधन और सिंगल साइन-ऑन समाधान तैनात करें।", "detail3": "जागरूकता बढ़ाने के लिए नियमित कर्मचारी सुरक्षा प्रशिक्षण आयोजित करें।", "detail4": "तेज़ खतरा प्रतिक्रिया के लिए सुरक्षा घटना प्रतिक्रिया योजनाएं स्थापित करें।"}, "socialEngineeringProtection": {"title": "सामाजिक इंजीनियरिंग हमला सुरक्षा रणनीतियां", "content": "सामाजिक इंजीनियरिंग हमले तकनीकी कमजोरियों के बजाय मानवीय मनोवैज्ञानिक कमजोरियों का फायदा उठाते हैं। हमलावर लक्षित व्यक्तियों को गोपनीय जानकारी प्रकट करने या विशिष्ट कार्य करने के लिए हेरफेर, धोखा या प्रेरित करते हैं। ये हमले अक्सर तकनीकी हमलों की तुलना में बचाव करना कठिन होते हैं क्योंकि वे विश्वास, डर और जिज्ञासा जैसी मानवीय भावनाओं का फायदा उठाते हैं।", "detail1": "सामान्य सामाजिक इंजीनियरिंग हमला तकनीकों और मनोवैज्ञानिक हेरफेर की पहचान करना सीखें।", "detail2": "सत्यापन प्रक्रियाएं स्थापित करें, स्वतंत्र चैनलों के माध्यम से संवेदनशील अनुरोधों की पुष्टि करें।", "detail3": "सोशल मीडिया और सार्वजनिक स्थानों पर व्यक्तिगत जानकारी के प्रदर्शन को सीमित करें।", "detail4": "संगठनों को सुरक्षा संस्कृति बनानी चाहिए और नियमित हमला सिमुलेशन परीक्षण आयोजित करना चाहिए।"}}, "common": {"backToHome": "होम पर वापस जाएं"}, "contactUs": {"title": "हमसे संपर्क करें", "subtitle": "कोई प्रश्न या सुझाव हैं? हम आपसे सुनना पसंद करेंगे।", "email": {"title": "ईमेल", "address": "<EMAIL>", "description": "हमें एक ईमेल भेजें और हम जल्द से जल्द आपको जवाब देंगे।"}, "support": {"title": "तकनीकी सहायता", "description": "यदि आपको हमारे पासवर्ड जेनरेटर का उपयोग करते समय कोई तकनीकी समस्या आती है, तो कृपया ईमेल के माध्यम से हमसे संपर्क करें। हमारी तकनीकी टीम पेशेवर सहायता प्रदान करेगी।"}, "feedback": {"title": "प्रतिक्रिया", "description": "आपकी प्रतिक्रिया हमारे लिए बहुत महत्वपूर्ण है। यदि आपके पास हमारी सेवा को बेहतर बनाने के लिए कोई सुझाव या विचार हैं, तो कृपया उन्हें हमारे साथ साझा करने में संकोच न करें।"}}, "faqPage": {"title": "अक्सर पूछे जाने वाले प्रश्न", "subtitle": "पासवर्ड सुरक्षा और हमारी सेवाओं के बारे में सामान्य प्रश्नों के उत्तर खोजें।"}, "helpCenter": {"title": "सहायता केंद्र", "subtitle": "जानें कि हमारे पासवर्ड जेनरेटर का उपयोग कैसे करें और अपने खाते की सुरक्षा कैसे करें।", "sections": {"gettingStarted": {"title": "शुरुआत करना", "content": "हमारा पासवर्ड जेनरेटर उपयोग करने में सरल और सहज है। बस पासवर्ड की लंबाई और वर्ण प्रकार की सेटिंग्स को समायोजित करें, फिर सुरक्षित पासवर्ड बनाने के लिए जेनरेट बटन पर क्लिक करें।"}, "passwordSecurity": {"title": "पासवर्ड सुरक्षा की सर्वोत्तम प्रथाएं", "content": "कम से कम 12 वर्णों के पासवर्ड का उपयोग करें, जिसमें बड़े और छोटे अक्षर, संख्याएं और विशेष वर्ण शामिल हों। प्रत्येक खाते के लिए अद्वितीय पासवर्ड का उपयोग करें और महत्वपूर्ण खातों के पासवर्ड को नियमित रूप से अपडेट करें।"}, "features": {"title": "सुविधाएं", "content": "हमारा उपकरण कस्टम पासवर्ड लंबाई (4-128 वर्ण), वर्ण प्रकार चयन (बड़े अक्षर, छोटे अक्षर, संख्याएं, विशेष वर्ण) का समर्थन करता है, और पासवर्ड शक्ति मूल्यांकन प्रदान करता है।"}, "privacy": {"title": "गोपनीयता सुरक्षा", "content": "सभी पासवर्ड आपके ब्राउज़र में स्थानीय रूप से जेनरेट होते हैं। हम कोई भी जेनरेट किए गए पासवर्ड को स्टोर या ट्रांसमिट नहीं करते हैं। आपकी गोपनीयता और सुरक्षा हमारी शीर्ष प्राथमिकताएं हैं।"}}}}