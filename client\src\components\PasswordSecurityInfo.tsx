import { useTranslation } from 'react-i18next';
import { Shield, AlertTriangle } from 'lucide-react';

const PasswordSecurityInfo = () => {
  const { t } = useTranslation();
  
  return (
    <section className="mb-16">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-4">
          {t('passwordSecurityInfo.title')}
        </h2>
      </div>
      
      <div className="grid md:grid-cols-2 gap-8">
        {/* What is a Strong Password */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="p-6 md:p-8">
            <div className="flex items-center mb-4">
              <div className="bg-green-100 inline-flex items-center justify-center w-12 h-12 rounded-full mr-4">
                <Shield className="text-green-600 text-xl" />
              </div>
              <h3 className="text-xl font-semibold">
                {t('passwordSecurityInfo.whatIsStrongPassword.title')}
              </h3>
            </div>
            <div className="text-dark-light leading-relaxed">
              {t('passwordSecurityInfo.whatIsStrongPassword.content')}
            </div>
          </div>
        </div>

        {/* Why is Password Security Important */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="p-6 md:p-8">
            <div className="flex items-center mb-4">
              <div className="bg-red-100 inline-flex items-center justify-center w-12 h-12 rounded-full mr-4">
                <AlertTriangle className="text-red-600 text-xl" />
              </div>
              <h3 className="text-xl font-semibold">
                {t('passwordSecurityInfo.whyImportant.title')}
              </h3>
            </div>
            <div className="text-dark-light leading-relaxed">
              {t('passwordSecurityInfo.whyImportant.content')}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PasswordSecurityInfo;
