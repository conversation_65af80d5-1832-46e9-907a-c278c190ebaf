import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import PasswordGenerator from '@/components/PasswordGenerator';
import PasswordSecurityInfo from '@/components/PasswordSecurityInfo';
import PasswordStrengthInfo from '@/components/PasswordStrengthInfo';
import FAQ from '@/components/FAQ';
import SecurityTips from '@/components/SecurityTips';

const Home = () => {
  const { t, i18n } = useTranslation();

  return (
    <>
      <Helmet>
        {/* 基本SEO元标签 */}
        <title>{t('meta.title')}</title>
        <meta name="description" content={t('meta.description')} />
        <meta name="keywords" content={t('meta.keywords', { defaultValue: "密码生成器, 随机密码, 安全密码, 强密码, 密码安全" })} />

        {/* 规范链接 - 防止重复内容 */}
        <link rel="canonical" href={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}`} />

        {/* Open Graph 标签 - 用于社交媒体分享 */}
        <meta property="og:title" content={t('meta.title')} />
        <meta property="og:description" content={t('meta.description')} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={`https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}`} />
        <meta property="og:image" content="https://password-generator.me/og-image.jpg" />
        <meta property="og:locale" content={i18n.language === 'en' ? 'en_US' :
                                           i18n.language === 'zh' ? 'zh_CN' :
                                           i18n.language === 'es' ? 'es_ES' :
                                           i18n.language === 'de' ? 'de_DE' :
                                           i18n.language === 'fr' ? 'fr_FR' :
                                           i18n.language === 'ja' ? 'ja_JP' :
                                           i18n.language === 'pt' ? 'pt_BR' :
                                           i18n.language === 'ru' ? 'ru_RU' :
                                           i18n.language === 'ar' ? 'ar_SA' :
                                           i18n.language === 'hi' ? 'hi_IN' : 'en_US'} />
        <meta property="og:site_name" content={t('footer.name')} />

        {/* Twitter 卡片标签 */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={t('meta.title')} />
        <meta name="twitter:description" content={t('meta.description')} />
        <meta name="twitter:image" content="https://password-generator.me/og-image.jpg" />
        <meta name="twitter:site" content="@password_generator_me" />
        <meta name="twitter:creator" content="@password_generator_me" />

        {/* 面包屑导航结构化数据 */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": t('footer.name'),
                "item": `https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}`
              }
            ]
          })}
        </script>

        {/* 多语言支持 - hreflang 标签 */}
        <link rel="alternate" hrefLang="en" href="https://password-generator.me/" />
        <link rel="alternate" hrefLang="zh" href="https://password-generator.me/zh" />
        <link rel="alternate" hrefLang="es" href="https://password-generator.me/es" />
        <link rel="alternate" hrefLang="de" href="https://password-generator.me/de" />
        <link rel="alternate" hrefLang="fr" href="https://password-generator.me/fr" />
        <link rel="alternate" hrefLang="ja" href="https://password-generator.me/ja" />
        <link rel="alternate" hrefLang="pt" href="https://password-generator.me/pt" />
        <link rel="alternate" hrefLang="ru" href="https://password-generator.me/ru" />
        <link rel="alternate" hrefLang="ar" href="https://password-generator.me/ar" />
        <link rel="alternate" hrefLang="hi" href="https://password-generator.me/hi" />
        <link rel="alternate" hrefLang="x-default" href="https://password-generator.me/" />

        {/* JSON-LD 结构化数据 - 增强搜索结果显示 */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": t('meta.title'),
            "description": t('meta.description'),
            "url": `https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}`,
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "author": {
              "@type": "Organization",
              "name": t('footer.name'),
              "url": "https://password-generator.me"
            },
            "publisher": {
              "@type": "Organization",
              "name": t('footer.name'),
              "logo": {
                "@type": "ImageObject",
                "url": "https://password-generator.me/logo.png"
              }
            },
            "potentialAction": {
              "@type": "UseAction",
              "target": `https://password-generator.me${i18n.language === 'en' ? '' : `/${i18n.language}`}`
            },
            "inLanguage": [
              {
                "@type": "Language",
                "name": "English",
                "alternateName": "en"
              },
              {
                "@type": "Language",
                "name": "Chinese",
                "alternateName": "zh"
              },
              {
                "@type": "Language",
                "name": "Spanish",
                "alternateName": "es"
              },
              {
                "@type": "Language",
                "name": "German",
                "alternateName": "de"
              },
              {
                "@type": "Language",
                "name": "French",
                "alternateName": "fr"
              },
              {
                "@type": "Language",
                "name": "Japanese",
                "alternateName": "ja"
              },
              {
                "@type": "Language",
                "name": "Portuguese",
                "alternateName": "pt"
              },
              {
                "@type": "Language",
                "name": "Russian",
                "alternateName": "ru"
              },
              {
                "@type": "Language",
                "name": "Arabic",
                "alternateName": "ar"
              },
              {
                "@type": "Language",
                "name": "Hindi",
                "alternateName": "hi"
              }
            ]
          })}
        </script>
      </Helmet>

      <div className="flex flex-col min-h-screen">
        <Header />

        <main className="container mx-auto px-4 py-10 max-w-4xl flex-grow">
          {/* Hero Section */}
          <section className="text-center mb-12">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              {t('home.title')}
            </h1>
            <p className="text-lg text-dark-light max-w-2xl mx-auto">
              {t('home.subtitle')}
            </p>
          </section>

          {/* Password Generator Component */}
          <PasswordGenerator />

          {/* Password Security Info Component */}
          <PasswordSecurityInfo />

          {/* Password Strength Info Component */}
          <PasswordStrengthInfo />

          {/* Security Image - using SVG for better accessibility and performance */}
          <section className="mb-16">
            <div className="rounded-xl overflow-hidden shadow-md bg-white p-8">
              <svg
                viewBox="0 0 800 250"
                xmlns="http://www.w3.org/2000/svg"
                className="w-full h-auto"
                aria-hidden="true"
              >
                <defs>
                  <linearGradient id="bg-grad" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#f0f4f9" />
                    <stop offset="100%" stopColor="#e1e7ed" />
                  </linearGradient>
                </defs>
                <rect width="800" height="250" fill="url(#bg-grad)" />
                <g transform="translate(100, 50)">
                  <circle cx="50" cy="50" r="40" fill="#0086ff" opacity="0.2" />
                  <path d="M50,30 L50,70 M30,50 L70,50" stroke="#0086ff" strokeWidth="4" />
                </g>
                <g transform="translate(250, 60)">
                  <rect x="0" y="0" width="80" height="120" rx="10" fill="#fff" stroke="#0086ff" strokeWidth="2" />
                  <rect x="10" y="10" width="60" height="10" rx="3" fill="#f0f4f9" />
                  <rect x="10" y="30" width="60" height="10" rx="3" fill="#f0f4f9" />
                  <rect x="10" y="50" width="60" height="10" rx="3" fill="#f0f4f9" />
                  <rect x="10" y="70" width="60" height="10" rx="3" fill="#f0f4f9" />
                  <rect x="20" y="90" width="40" height="20" rx="5" fill="#0086ff" />
                </g>
                <g transform="translate(400, 70)">
                  <path d="M40,0 L80,30 L40,60 L0,30 Z" fill="#0086ff" opacity="0.2" />
                  <path d="M40,10 L40,50 M20,30 L60,30" stroke="#0086ff" strokeWidth="4" />
                </g>
                <g transform="translate(550, 50)">
                  <circle cx="50" cy="50" r="40" fill="none" stroke="#0086ff" strokeWidth="2" />
                  <path d="M35,65 L65,35 M35,35 L65,65" stroke="#0086ff" strokeWidth="2" />
                </g>
              </svg>
            </div>
          </section>

          {/* FAQ Component */}
          <FAQ />

          {/* Security Tips Component */}
          <SecurityTips />
        </main>

        <Footer />
      </div>
    </>
  );
};

export default Home;
