import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import { Mail, MessageCircle, HelpCircle } from 'lucide-react';
import BackButton from '@/components/ui/BackButton';

const ContactUs = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Helmet>
        <title>{t('contactUs.title')} - {t('footer.name')}</title>
        <meta name="description" content={t('contactUs.subtitle')} />
      </Helmet>
      
      <div className="container mx-auto px-4 py-8">
        <BackButton />
        
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {t('contactUs.title')}
            </h1>
            <p className="text-xl text-gray-600">
              {t('contactUs.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Email Contact */}
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="flex justify-center mb-4">
                <Mail className="h-12 w-12 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">
                {t('contactUs.email.title')}
              </h3>
              <p className="text-gray-600 mb-4">
                {t('contactUs.email.description')}
              </p>
              <a 
                href="mailto:<EMAIL>"
                className="inline-flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                {t('contactUs.email.address')}
              </a>
            </div>

            {/* Technical Support */}
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="flex justify-center mb-4">
                <HelpCircle className="h-12 w-12 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">
                {t('contactUs.support.title')}
              </h3>
              <p className="text-gray-600">
                {t('contactUs.support.description')}
              </p>
            </div>

            {/* Feedback */}
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="flex justify-center mb-4">
                <MessageCircle className="h-12 w-12 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">
                {t('contactUs.feedback.title')}
              </h3>
              <p className="text-gray-600">
                {t('contactUs.feedback.description')}
              </p>
            </div>
          </div>

          {/* Additional Contact Information */}
          <div className="mt-12 bg-white rounded-lg shadow-lg p-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {t('contactUs.email.title')}
              </h2>
              <p className="text-gray-600 mb-6">
                {t('contactUs.email.description')}
              </p>
              <div className="inline-flex items-center space-x-2 text-lg">
                <Mail className="h-5 w-5 text-primary" />
                <a 
                  href="mailto:<EMAIL>"
                  className="text-primary hover:text-primary-dark transition-colors font-medium"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactUs;
