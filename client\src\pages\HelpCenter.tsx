import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet';
import { BookO<PERSON>, Shield, Settings, Lock } from 'lucide-react';
import BackButton from '@/components/ui/BackButton';

const HelpCenter = () => {
  const { t } = useTranslation();

  const helpSections = [
    {
      icon: BookOpen,
      title: t('helpCenter.sections.gettingStarted.title'),
      content: t('helpCenter.sections.gettingStarted.content')
    },
    {
      icon: Shield,
      title: t('helpCenter.sections.passwordSecurity.title'),
      content: t('helpCenter.sections.passwordSecurity.content')
    },
    {
      icon: Settings,
      title: t('helpCenter.sections.features.title'),
      content: t('helpCenter.sections.features.content')
    },
    {
      icon: Lock,
      title: t('helpCenter.sections.privacy.title'),
      content: t('helpCenter.sections.privacy.content')
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Helmet>
        <title>{t('helpCenter.title')} - {t('footer.name')}</title>
        <meta name="description" content={t('helpCenter.subtitle')} />
      </Helmet>
      
      <div className="container mx-auto px-4 py-8">
        <BackButton />
        
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {t('helpCenter.title')}
            </h1>
            <p className="text-xl text-gray-600">
              {t('helpCenter.subtitle')}
            </p>
          </div>

          <div className="grid gap-8">
            {helpSections.map((section, index) => {
              const IconComponent = section.icon;
              return (
                <div key={index} className="bg-white rounded-lg shadow-lg p-8">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <IconComponent className="h-8 w-8 text-primary" />
                    </div>
                    <div className="flex-1">
                      <h2 className="text-2xl font-bold text-gray-900 mb-4">
                        {section.title}
                      </h2>
                      <p className="text-gray-600 leading-relaxed">
                        {section.content}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Additional Help Resources */}
          <div className="mt-12 bg-white rounded-lg shadow-lg p-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Need More Help?
              </h2>
              <p className="text-gray-600 mb-6">
                If you can't find what you're looking for, feel free to contact us.
              </p>
              <div className="flex justify-center space-x-4">
                <a 
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
                >
                  Contact Support
                </a>
                <a 
                  href="/faq"
                  className="inline-flex items-center px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-colors"
                >
                  View FAQ
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HelpCenter;
