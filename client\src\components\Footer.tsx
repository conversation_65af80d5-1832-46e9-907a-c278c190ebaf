import { useTranslation } from 'react-i18next';
import { <PERSON> } from 'wouter';
import { Shield } from 'lucide-react';

const Footer = () => {
  const { t } = useTranslation();

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-dark text-white py-10">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row justify-center lg:justify-between items-center mb-8 gap-8">
          {/* Logo and Tagline - Left */}
          <div className="text-center lg:text-left mb-6 lg:mb-0 lg:ml-8">
            <div className="flex items-center justify-center lg:justify-start space-x-2 mb-4">
              <Shield className="text-primary h-6 w-6" />
              <span className="font-bold text-xl">{t('footer.name')}</span>
            </div>
            <p className="text-gray-400 max-w-xs">
              {t('footer.tagline')}
            </p>
          </div>

          {/* Main Navigation - Center */}
          <div className="flex flex-col items-center">
            <h3 className="text-sm font-semibold uppercase tracking-wider mb-4 text-primary">
              {t('footer.resources.title')}
            </h3>
            <ul className="space-y-2 text-center">
              <li>
                <Link href="/contact" className="text-gray-400 hover:text-white transition-colors">
                  {t('footer.resources.contactUs')}
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-gray-400 hover:text-white transition-colors">
                  {t('footer.resources.faq')}
                </Link>
              </li>
              <li>
                <Link href="/help-center" className="text-gray-400 hover:text-white transition-colors">
                  {t('footer.resources.helpCenter')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal Links - Right */}
          <div className="flex flex-col items-center lg:items-end lg:mr-8">
            <h3 className="text-sm font-semibold uppercase tracking-wider mb-4 text-primary">
              {t('footer.company.title')}
            </h3>
            <ul className="space-y-2 text-center lg:text-right">
              <li>
                <Link href="/privacy-policy" className="text-gray-400 hover:text-white transition-colors">
                  {t('footer.company.privacyPolicy')}
                </Link>
              </li>
              <li>
                <Link href="/terms-of-service" className="text-gray-400 hover:text-white transition-colors">
                  {t('footer.company.terms')}
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-700 pt-8 flex justify-center">
          <p className="text-gray-400 text-center">
            &copy; {currentYear} {t('footer.name')}. {t('footer.copyright')}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
